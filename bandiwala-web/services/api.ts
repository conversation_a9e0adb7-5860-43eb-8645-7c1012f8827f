import axios, { AxiosError } from 'axios';
import { CartResponse } from '@/types/cart';

// Get the API URL from environment variables
// const API_URL = process.env.NEXT_PUBLIC_API_URL
//   ? `${process.env.NEXT_PUBLIC_API_URL}/`
//   : 'http://localhost:4000/';
const API_URL = 'http://localhost:4000/';

// Log the API URL for debug
console.log('Using API URL:', API_URL);

// Create an axios instance with base configuration
const api = axios.create({
  // Use the API URL from environment variables
  baseURL: API_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
  validateStatus: (status) => {
    return status >= 200 && status < 500;
  },
  withCredentials: true,
  xsrfCookieName: 'XSRF-TOKEN',
  xsrfHeaderName: 'X-XSRF-TOKEN',
});

// Add request interceptor for logging and auth
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);

    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }

    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for logging and auth error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);

    // Handle authentication errors
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      console.warn('Authentication error detected, checking token validity');

      // Check if we have a token
      const token = localStorage.getItem('auth_token');
      if (token) {
        try {
          // Try to decode the token to check if it's expired
          const base64Payload = token.split('.')[1];
          const payload = JSON.parse(Buffer.from(base64Payload, 'base64').toString());
          const isExpired = payload.exp * 1000 < Date.now();

          if (isExpired) {
            console.warn('Token is expired, clearing authentication data');
            // Clear token from localStorage
            localStorage.removeItem('auth_token');
            // Clear token from API headers
            if (api.defaults.headers) {
              delete api.defaults.headers.common['Authorization'];
            }
            // Clear token from cookies
            document.cookie = 'token=; path=/; max-age=0; SameSite=Strict';

            // Redirect to login page if we're in the browser
            if (typeof window !== 'undefined') {
              window.location.href = '/login';
            }
          }
        } catch (e) {
          console.error('Error decoding token:', e);
        }
      }
    }

    return Promise.reject(error);
  }
);

// Retry configuration
const RETRY_COUNT = 3;
const RETRY_DELAY = 1000;

const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const retryRequest = async <T>(
  fn: () => Promise<T>,
  retries: number = RETRY_COUNT
): Promise<T> => {
  try {
    return await fn();
  } catch (error: any) {
    if (retries === 0 || error.response?.status === 404) {
      throw error;
    }
    await sleep(RETRY_DELAY);
    return retryRequest(fn, retries - 1);
  }
};

// Helper function to get auth token or user ID header
export const getAuthHeaders = async (sessionToken?: string, mongoUserId?: string) => {
  const headers: Record<string, string> = {};

  // Add authorization token if available
  if (sessionToken) {
    headers['Authorization'] = `Bearer ${sessionToken}`;
  }

  // Add MongoDB user ID if available
  if (mongoUserId) {
    headers['x-user-id'] = mongoUserId;
  }

  return headers;
};

// Helper function to handle API errors
const handleApiError = (error: ApiError, operation: string) => {
  console.error(`Error during ${operation}:`, error);

  if (error.code === 'ECONNABORTED') {
    return {
      success: false,
      message: 'Request timed out. Please try again.',
      error: 'TIMEOUT'
    };
  }
  if (error.message?.includes('Network Error')) {
    console.warn('Network error detected - check if backend server is running');
    return {
      success: false,
      message: 'Network error. Please check your connection.',
      error: 'NETWORK'
    };
  }
  if (error.response?.status === 401) {
    console.warn('Authentication error - user may need to log in');
    return {
      success: false,
      message: 'Authentication error. Please log in again.',
      error: 'AUTH'
    };
  }

  return {
    success: false,
    message: `Failed to ${operation}`,
    error: error.message || 'Unknown error'
  };
};

// API services
export const vendorService = {
  getAllVendors: async () => {
    try {
      // Try to get data from our new backend API
      const response = await api.get('/api/vendors');

      // If the response has data, return it
      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          data: response.data.data
        };
      }

      // If no data, fall back to the static API
      console.log('No data from backend API, falling back to static API');
      const staticResponse = await api.get('/api/static/vendors');
      return staticResponse.data;
    } catch (error) {
      console.error('Error fetching vendors from backend API, falling back to static API', error);
      // If there's an error, fall back to the static API
      try {
        const staticResponse = await api.get('/api/static/vendors');
        return staticResponse.data;
      } catch (staticError) {
        console.error('Static API also failed', staticError);
        return {
          success: false,
          message: 'Failed to fetch vendors',
          data: []
        };
      }
    }
  },

  getVendorBySlug: async (slug: string) => {
    try {
      console.log(`Attempting to fetch vendor with slug: ${slug} from backend API`);
      const response = await api.get(`/api/vendors/slug/${slug}`);

      // Check if we got a valid response with vendor data
      if (response.data && response.data.success && response.data.data) {
        console.log(`Successfully fetched vendor with slug: ${slug} from backend API`);
        return {
          success: true,
          data: response.data.data
        };
      }

      console.log(`No valid vendor data from backend API for slug: ${slug}, falling back to static API`);
      const staticResponse = await api.get(`/api/vendors/${slug}`);

      // Validate static response
      if (!staticResponse.data || !staticResponse.data.success || !staticResponse.data.data || !staticResponse.data.data.vendor) {
        console.error(`Static API also failed to return valid vendor data for slug: ${slug}`);
        throw new Error(`Vendor with slug ${slug} not found in both backend and static APIs`);
      }

      console.log(`Successfully fetched vendor with slug: ${slug} from static API`);
      return staticResponse.data;
    } catch (error) {
      console.error(`Error fetching vendor by slug: ${slug}`, error);

      try {
        console.log(`Attempting to fetch from static API as fallback for slug: ${slug}`);
        const staticResponse = await api.get(`/api/vendors/${slug}`);
        return staticResponse.data;
      } catch (staticError) {
        console.error(`Static API fallback also failed for slug: ${slug}`, staticError);
        throw new Error(`Failed to fetch vendor with slug: ${slug} from both APIs`);
      }
    }
  },

  getVendorById: async (id: string) => {
    try {
      console.log(`Attempting to fetch vendor with ID: ${id}`);
      const response = await api.get(`/api/vendors/${id}`);

      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          data: response.data.data
        };
      }

      throw new Error(`Vendor with ID ${id} not found`);
    } catch (error) {
      console.error(`Error fetching vendor by ID: ${id}`, error);
      throw error;
    }
  },

  searchVendors: async (query: string) => {
    try {
      console.log(`Searching vendors with query: ${query}`);
      const response = await api.get(`/api/vendors/search?query=${encodeURIComponent(query)}`);

      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'Failed to search vendors',
        data: []
      };
    } catch (error) {
      console.error(`Error searching vendors: ${query}`, error);
      return {
        success: false,
        message: 'Failed to search vendors',
        data: []
      };
    }
  }
};

export const menuItemService = {
  getAllMenuItems: async () => {
    try {
      // Try to get data from our new backend API
      const response = await api.get('/api/menu-items');

      // If the response has data, return it
      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          data: response.data.data
        };
      }

      // If no data, fall back to the static API
      console.log('No data from backend API, falling back to static API');
      const staticResponse = await api.get('/api/static/menu-items');
      return staticResponse.data;
    } catch (error) {
      console.error('Error fetching menu items from backend API, falling back to static API', error);
      // If there's an error, fall back to the static API
      try {
        const staticResponse = await api.get('/api/static/menu-items');
        return staticResponse.data;
      } catch (staticError) {
        console.error('Static API also failed', staticError);
        return {
          success: false,
          message: 'Failed to fetch menu items',
          data: []
        };
      }
    }
  },

  getMenuItemById: async (id: string) => {
    try {
      console.log(`Fetching menu item with ID: ${id}`);
      const response = await api.get(`/api/menu-items/${id}`);

      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          data: response.data.data
        };
      }

      throw new Error(`Menu item with ID ${id} not found`);
    } catch (error) {
      console.error('Error fetching menu item by ID, falling back to static API', error);
      try {
        const staticResponse = await api.get(`/api/menu-items/${id}`);
        return staticResponse.data;
      } catch (staticError) {
        console.error('Static API also failed', staticError);
        throw new Error(`Failed to fetch menu item with ID: ${id}`);
      }
    }
  },

  getMenuItemBySlug: async (slug: string) => {
    try {
      console.log(`Fetching menu item with slug: ${slug} from backend API`);
      const response = await api.get(`/api/menu-items/slug/${slug}`);

      if (response.data && response.data.success && response.data.data) {
        console.log(`Successfully fetched menu item with slug: ${slug} from backend API`);
        return {
          success: true,
          data: response.data.data
        };
      }

      console.log(`No valid menu item data from backend API for slug: ${slug}, falling back to static API`);
      const staticResponse = await api.get(`/api/static/menu-items/${slug}`);

      if (staticResponse.data && staticResponse.data.success && staticResponse.data.data) {
        console.log(`Successfully fetched menu item with slug: ${slug} from static API`);
        return staticResponse.data;
      }

      throw new Error(`Menu item with slug ${slug} not found in both backend and static APIs`);
    } catch (error) {
      console.error('Error fetching menu item by slug from backend API:', error);

      try {
        console.log(`Attempting to fetch from static API as fallback for slug: ${slug}`);
        const staticResponse = await api.get(`/api/static/menu-items/${slug}`);

        if (staticResponse.data && staticResponse.data.success && staticResponse.data.data) {
          console.log(`Successfully fetched menu item with slug: ${slug} from static API fallback`);
          return staticResponse.data;
        }

        throw new Error(`Menu item with slug ${slug} not found in static API`);
      } catch (staticError) {
        console.error(`Static API fallback also failed for slug: ${slug}`, staticError);
        throw new Error(`Failed to fetch menu item with slug: ${slug} from both APIs`);
      }
    }
  },

  getMenuItemsByVendor: async (vendorId: string) => {
    try {
      console.log(`Fetching menu items for vendor: ${vendorId}`);
      const response = await api.get(`/api/menu-items/vendor/${vendorId}`);

      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'No menu items found for this vendor',
        data: []
      };
    } catch (error) {
      console.error('Error fetching menu items by vendor, falling back to static API', error);
      try {
        const staticResponse = await api.get(`/api/menu-items/vendor/${vendorId}`);
        return staticResponse.data;
      } catch (staticError) {
        console.error('Static API also failed', staticError);
        return {
          success: false,
          message: 'Failed to fetch menu items for this vendor',
          data: []
        };
      }
    }
  },

  getMenuItemsByCategory: async (category: string) => {
    try {
      console.log(`Fetching menu items for category: ${category}`);
      const response = await api.get(`/api/menu-items/category/${encodeURIComponent(category)}`);

      if (response.data && response.data.success && response.data.data) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'No menu items found for this category',
        data: []
      };
    } catch (error) {
      console.error(`Error fetching menu items by category: ${category}`, error);
      return {
        success: false,
        message: 'Failed to fetch menu items for this category',
        data: []
      };
    }
  },

  searchMenuItems: async (query: string) => {
    try {
      console.log(`Searching menu items with query: ${query}`);
      const response = await api.get(`/api/menu-items/search?query=${encodeURIComponent(query)}`);

      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'No menu items found for your search',
        data: []
      };
    } catch (error) {
      console.error(`Error searching menu items: ${query}`, error);
      return {
        success: false,
        message: 'Failed to search menu items',
        data: []
      };
    }
  }
};

// Review service
export const reviewService = {
  // Create a new review (verified purchase only)
  createReview: async (reviewData: {
    targetType: 'MenuItem' | 'Vendor';
    targetId: string;
    rating: number;
    comment: string;
    orderId: string;
  }) => {
    try {
      console.log('Creating review:', reviewData);
      const response = await api.post('/api/reviews', reviewData);

      if (response.data && response.data.success) {
        return {
          success: true,
          message: response.data.message,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: response.data?.message || 'Failed to create review'
      };
    } catch (error: any) {
      console.error('Error creating review:', error);
      return handleApiError(error, 'create review');
    }
  },

  // Get reviews for a target (MenuItem or Vendor)
  getReviewsByTarget: async (
    targetType: 'MenuItem' | 'Vendor',
    targetId: string,
    page: number = 1,
    limit: number = 10
  ) => {
    try {
      console.log(`Fetching reviews for ${targetType} ${targetId}`);
      const response = await api.get(
        `/api/reviews/${targetType}/${targetId}?page=${page}&limit=${limit}`
      );

      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'Failed to fetch reviews',
        data: {
          reviews: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalReviews: 0,
            hasNextPage: false,
            hasPrevPage: false
          },
          stats: {
            averageRating: 0,
            totalReviews: 0
          }
        }
      };
    } catch (error: any) {
      console.error('Error fetching reviews:', error);
      return {
        success: false,
        message: 'Failed to fetch reviews',
        data: {
          reviews: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalReviews: 0,
            hasNextPage: false,
            hasPrevPage: false
          },
          stats: {
            averageRating: 0,
            totalReviews: 0
          }
        }
      };
    }
  },

  // Get user's reviews
  getUserReviews: async (page: number = 1, limit: number = 10) => {
    try {
      console.log('Fetching user reviews');
      const response = await api.get(
        `/api/reviews/user/my-reviews?page=${page}&limit=${limit}`
      );

      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'Failed to fetch user reviews',
        data: {
          reviews: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalReviews: 0,
            hasNextPage: false,
            hasPrevPage: false
          }
        }
      };
    } catch (error: any) {
      console.error('Error fetching user reviews:', error);
      return handleApiError(error, 'fetch user reviews');
    }
  },

  // Update a review
  updateReview: async (reviewId: string, updateData: {
    rating?: number;
    comment?: string;
  }) => {
    try {
      console.log('Updating review:', reviewId, updateData);
      const response = await api.put(`/api/reviews/${reviewId}`, updateData);

      if (response.data && response.data.success) {
        return {
          success: true,
          message: response.data.message,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: response.data?.message || 'Failed to update review'
      };
    } catch (error: any) {
      console.error('Error updating review:', error);
      return handleApiError(error, 'update review');
    }
  },

  // Delete a review
  deleteReview: async (reviewId: string) => {
    try {
      console.log('Deleting review:', reviewId);
      const response = await api.delete(`/api/reviews/${reviewId}`);

      if (response.data && response.data.success) {
        return {
          success: true,
          message: response.data.message
        };
      }

      return {
        success: false,
        message: response.data?.message || 'Failed to delete review'
      };
    } catch (error: any) {
      console.error('Error deleting review:', error);
      return handleApiError(error, 'delete review');
    }
  },

  // Flag a review for moderation
  flagReview: async (reviewId: string, reason: string) => {
    try {
      console.log('Flagging review:', reviewId, reason);
      const response = await api.post(`/api/reviews/${reviewId}/flag`, { reason });

      if (response.data && response.data.success) {
        return {
          success: true,
          message: response.data.message
        };
      }

      return {
        success: false,
        message: response.data?.message || 'Failed to flag review'
      };
    } catch (error: any) {
      console.error('Error flagging review:', error);
      return handleApiError(error, 'flag review');
    }
  },

  // Moderate a review (admin only)
  moderateReview: async (reviewId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      console.log('Moderating review:', reviewId, action, reason);
      const response = await api.put(`/api/reviews/${reviewId}/moderate`, { action, reason });

      if (response.data && response.data.success) {
        return {
          success: true,
          message: response.data.message,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: response.data?.message || 'Failed to moderate review'
      };
    } catch (error: any) {
      console.error('Error moderating review:', error);
      return handleApiError(error, 'moderate review');
    }
  },

  // Get pending reviews for moderation (admin only)
  getPendingReviews: async (page: number = 1, limit: number = 10) => {
    try {
      console.log('Fetching pending reviews for moderation');
      const response = await api.get(
        `/api/reviews/moderation/pending?page=${page}&limit=${limit}`
      );

      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'Failed to fetch pending reviews',
        data: {
          reviews: [],
          pagination: {
            currentPage: 1,
            totalPages: 0,
            totalReviews: 0,
            hasNextPage: false,
            hasPrevPage: false
          }
        }
      };
    } catch (error: any) {
      console.error('Error fetching pending reviews:', error);
      return handleApiError(error, 'fetch pending reviews');
    }
  },

  // Get order review status (which items can be reviewed from an order)
  getOrderReviewStatus: async (orderId: string) => {
    try {
      console.log('Fetching order review status for order:', orderId);
      const response = await api.get(`/api/reviews/order/${orderId}/status`);

      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'Failed to fetch order review status',
        data: null
      };
    } catch (error: any) {
      console.error('Error fetching order review status:', error);
      return handleApiError(error, 'fetch order review status');
    }
  }
};

// Define error type for better type safety
interface ApiError {
  code?: string;
  message?: string;
  response?: {
    status?: number;
    data?: {
      success?: boolean;
      message?: string;
      [key: string]: unknown;
    };
  };
}

// Define cart response type for better type safety
interface RequestStatus {
  isLoading: boolean;
  retryCount: number;
  lastError: Error | null;
}

const requestStatus: RequestStatus = {
  isLoading: false,
  retryCount: 0,
  lastError: null
};

const handleCartError = (error: AxiosError): CartResponse => {
  console.error('Cart API error:', error);

  // Default error response
  const errorResponse: CartResponse = {
    success: false,
    message: 'An error occurred while processing your request',
    useLocalStorage: true,
    data: {
      userId: 'guest-user',
      items: []
    }
  };

  // Log more detailed error information
  if (error.response) {
    console.error('Error response data:', error.response.data);
    console.error('Error response status:', error.response.status);
    console.error('Error response headers:', error.response.headers);

    // Extract error message from response if available
    if (error.response.data && typeof error.response.data === 'object') {
      const responseData = error.response.data as any;
      if (responseData.message) {
        errorResponse.message = responseData.message;
      }
      if (responseData.error) {
        errorResponse.error = {
          code: responseData.error.code || 'UNKNOWN_ERROR',
          message: responseData.error.message || errorResponse.message
        };
      }
    }

    // Handle different status codes
    switch (error.response.status) {
      case 400:
        errorResponse.message = 'Invalid request. Please check your inputs.';
        errorResponse.useLocalStorage = false; // Don't fall back to local storage for validation errors
        break;
      case 401:
        errorResponse.message = 'Authentication required. Please sign in.';
        // Store the current URL to redirect back after login
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_redirect', window.location.pathname);
        }
        break;
      case 403:
        errorResponse.message = 'You do not have permission to perform this action.';
        break;
      case 404:
        errorResponse.message = 'Item not found.';
        break;
      case 409:
        errorResponse.message = 'Conflict with current state. Please refresh and try again.';
        break;
      case 422:
        errorResponse.message = 'Validation error. Please check your inputs.';
        errorResponse.useLocalStorage = false; // Don't fall back to local storage for validation errors
        break;
      case 429:
        errorResponse.message = 'Too many requests. Please try again later.';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        errorResponse.message = 'Server error. Please try again later.';
        break;
    }
  } else if (error.request) {
    console.error('Error request:', error.request);
    errorResponse.message = 'No response from server. Please check your connection.';
  } else {
    console.error('Error message:', error.message);
    errorResponse.message = error.message || 'Unknown error occurred';
  }

  console.error('Error config:', error.config);

  // Special handling for timeout errors
  if (error.code === 'ECONNABORTED' || (error.message && error.message.includes('timeout'))) {
    console.warn('Request timeout, falling back to local storage');
    errorResponse.message = 'Request timed out. Please try again later.';
  }

  // Add timestamp to error for tracking
  errorResponse.error = {
    code: errorResponse.error?.code || 'UNKNOWN_ERROR',
    message: errorResponse.error?.message || errorResponse.message || 'Unknown error',
    timestamp: new Date().toISOString()
  };

  console.warn('Returning error response:', errorResponse);
  return errorResponse;
};

export const cartService = {
  getCart: async (token?: string): Promise<CartResponse> => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Getting cart with auth headers:', headers);

      // Use a try-catch to handle the case where the cart endpoint is not available yet
      try {
        const response = await api.get<CartResponse>('/api/cart', { headers });
        return response.data;
      } catch (cartError) {
        console.warn('Cart API not available, returning empty cart');
        // Return a default empty cart response
        return {
          success: true,
          message: 'Empty cart',
          data: {
            userId: 'guest-user',
            items: []
          }
        };
      }
    } catch (error) {
      return handleCartError(error as AxiosError);
    }
  },

  addToCart: async (menuItemId: string, quantity: number = 1, notes?: string, token?: string, plate: string = 'full', userLocation?: { lat: number; lng: number }): Promise<CartResponse> => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Adding to cart with auth headers:', headers);

      try {
        const response = await api.post<CartResponse>('/api/cart', {
          menuItemId,
          quantity,
          notes,
          plate, 
          userLocation
        }, { headers });
        return response.data;
      } catch (cartError) {
        console.warn('Cart API not available, will use local storage fallback');
        // Don't return empty cart - let the calling code handle the fallback
        throw cartError;
      }
    } catch (error) {
      return handleCartError(error as AxiosError);
    }
  },

  updateCartItem: async (menuItemId: string, quantity: number, notes?: string, token?: string, plate?: string): Promise<CartResponse> => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Updating cart with auth headers:', headers);

      try {
        const response = await api.put<CartResponse>('/api/cart', {
          menuItemId,
          quantity,
          notes,
          plate // Include the plate parameter if provided
        }, { headers });
        return response.data;
      } catch (cartError) {
        console.warn('Cart API not available, will use local storage fallback');
        // Don't return empty cart - let the calling code handle the fallback
        throw cartError;
      }
    } catch (error) {
      return handleCartError(error as AxiosError);
    }
  },

  removeFromCart: async (menuItemId: string, token?: string, plate?: string): Promise<CartResponse> => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Removing from cart with auth headers:', headers);

      try {
        // Add plate as a query parameter if provided
        const url = plate
          ? `/api/cart/${menuItemId}?plate=${encodeURIComponent(plate)}`
          : `/api/cart/${menuItemId}`;

        const response = await api.delete<CartResponse>(url, { headers });
        return response.data;
      } catch (cartError) {
        console.warn('Cart API not available, will use local storage fallback');
        // Don't return empty cart - let the calling code handle the fallback
        throw cartError;
      }
    } catch (error) {
      return handleCartError(error as AxiosError);
    }
  },

  clearCart: async (token?: string): Promise<CartResponse> => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Clearing cart with auth headers:', headers);

      try {
        const response = await api.delete<CartResponse>('/api/cart', { headers });
        return response.data;
      } catch (cartError) {
        console.warn('Cart API not available, will use local storage fallback');
        // For clear cart, we can return empty cart since that's the intended result
        return {
          success: true,
          message: 'Cart cleared (local)',
          data: {
            userId: 'guest-user',
            items: []
          }
        };
      }
    } catch (error) {
      return handleCartError(error as AxiosError);
    }
  },

  reorderCartItems: async (itemOrders: Array<{menuItemId: string, subcategoryTitle: string, order: number}>, token?: string): Promise<CartResponse> => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Reordering cart items with auth headers:', headers);

      try {
        const response = await api.put<CartResponse>('/api/cart/reorder', {
          itemOrders
        }, { headers });
        return response.data;
      } catch (cartError) {
        console.warn('Cart reorder API not available');
        throw cartError;
      }
    } catch (error) {
      return handleCartError(error as AxiosError);
    }
  }
};

export const orderService = {
  getUserOrders: async () => {
    try {
      // Try to connect to the backend first
      const response = await api.get('/api/orders');
      return response.data;
    } catch (error) {
      console.warn('Backend orders API not available, falling back to frontend API');
      // Fallback to frontend API if backend is not available
      const response = await api.get('/api/orders');
      return response.data;
    }
  },

  getOrderById: async (id: string) => {
    console.log('orderService.getOrderById called with id:', id);

    // Check if it's a temporary order ID
    if (id.startsWith('TEMP-')) {
      console.warn('Temporary order ID detected:', id);
      throw new Error('This is a temporary order ID used for email purposes only. The actual order may still be processing or may have failed. Please check your orders list for the confirmed order.');
    }

    try {
      // Try to connect to the backend first
      console.log('Trying backend API for order:', id);
      const response = await api.get(`/api/orders/${id}`);
      console.log('Backend API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.warn('Backend order detail API not available, falling back to frontend API');
      console.error('Backend API error:', error);

      // Check if the backend error is specifically about temporary order ID
      if (error.response?.data?.message?.includes('temporary order ID')) {
        throw new Error(error.response.data.message);
      }

      // Fallback to frontend API if backend is not available
      try {
        console.log('Trying frontend API for order:', id);
        const response = await api.get(`/api/orders/${id}`);
        console.log('Frontend API response:', response.data);
        return response.data;
      } catch (frontendError) {
        console.error('Frontend API error:', frontendError);
        throw frontendError;
      }
    }
  },

  createOrder: async (orderData: {
    deliveryAddress: {
      formatted: string;
      coordinates: {
        lat: number;
        lng: number;
      };
      mapUrl?: string;
    };
    paymentMethod: 'cash' | 'card' | 'upi';
    promoCode?: string;
  }) => {
    // NOTE: For payment flows, order creation is now handled automatically
    // in the backend payment verification process. This function is kept
    // for backward compatibility and non-payment order creation scenarios.
    const response = await api.post('/api/orders', orderData);
    return response.data;
  },

  cancelOrder: async (id: string) => {
    try {
      // Try to connect to the backend first
      const response = await api.put(`/api/orders/${id}/cancel`);
      return response.data;
    } catch (error) {
      console.warn('Backend cancel order API not available, falling back to frontend API');
      // Fallback to frontend API if backend is not available
      const response = await api.put(`/api/orders/${id}/cancel`);
      return response.data;
    }
  },

  // Get order timer (remaining time until delivery)
  getOrderTimer: async (orderId: string) => {
    try {
      console.log('Fetching order timer for order:', orderId);
      const response = await api.get(`/api/orders/${orderId}/timer`);

      if (response.data && response.data.success) {
        return {
          success: true,
          data: response.data.data
        };
      }

      return {
        success: false,
        message: 'Failed to fetch order timer',
        data: null
      };
    } catch (error: any) {
      console.error('Error fetching order timer:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to fetch order timer',
        data: null
      };
    }
  },
};

export const promoCodeService = {
  validatePromoCode: async (code: string, subtotal: number) => {
    const response = await api.post('/api/promo-codes/validate', { code, subtotal });
    return response.data;
  },
};

export const authService = {
  registerUser: async (userData: {
    name: string;
    email: string;
    phone: string;
    password: string;
    verificationMethod: 'email' | 'phone';
  }) => {
    try {
      console.log('authService.registerUser: Starting registration with data:', { ...userData, password: '***' });

      // Call the frontend API route which will proxy to the backend
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      console.log('authService.registerUser: Response status:', response.status);
      console.log('authService.registerUser: Response ok:', response.ok);

      if (!response.ok) {
        console.error('authService.registerUser: HTTP error:', response.status, response.statusText);
        const errorText = await response.text();
        console.error('authService.registerUser: Error response body:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('authService.registerUser: Success response data:', data);
      return data;
    } catch (error) {
      console.error('authService.registerUser: Error registering user:', error);
      throw error;
    }
  },

  resendOTP: async (resendData: {
    email: string;
    phone: string;
    verificationMethod: 'email' | 'phone';
  }) => {
    try {
      console.log('Resending OTP with data:', resendData);
      const response = await fetch('/api/resend-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(resendData),
      });
      const data = await response.json();
      console.log('Resend OTP response:', data);
      return data;
    } catch (error) {
      console.error('Error resending OTP:', error);
      throw error;
    }
  },

  verifyOTP: async (verificationData: {
    email: string;
    phone: string;
    otp: string;
  }) => {
    try {
      console.log('Verifying OTP with data:', verificationData);
      const response = await fetch('/api/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(verificationData),
      });
      const data = await response.json();
      console.log('OTP verification response:', data);

      // If successful, store the token
      if (data.success && data.token) {
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_token', data.token);

          // Also set the token in the API headers
          if (api.defaults.headers) {
            api.defaults.headers.common['Authorization'] = `Bearer ${data.token}`;
            console.log('Token set in API headers after OTP verification');
          }
        }
      }

      return data;
    } catch (error) {
      console.error('Error verifying OTP:', error);
      throw error;
    }
  },

  login: async (credentials: { email?: string; phone?: string; password: string }) => {
    try {
      const identifier = credentials.email ? `email: ${credentials.email}` : `phone: ${credentials.phone}`;
      console.log('Logging in with', identifier);
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });
      const data = await response.json();
      console.log('Login response:', data);

      // Check user role before proceeding
      if (data.success && data.user && data.user.role && data.user.role !== "user") {
        console.log('authService.login: User has non-user role:', data.user.role, 'Rejecting login');
        throw new Error('Access denied: This application is only for regular users');
      }

      // If successful, store the token
      if (data.success && data.token) {
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_token', data.token);

          // Also set the token in the API headers
          if (api.defaults.headers) {
            api.defaults.headers.common['Authorization'] = `Bearer ${data.token}`;
            console.log('Token set in API headers after login');
          }

          // Also set the token in document cookie for API routes
          document.cookie = `token=${data.token}; path=/; max-age=604800; SameSite=Strict`;
          console.log('Token set in cookie for API routes after login');
        }
      }

      return data;
    } catch (error) {
      console.error('Error logging in:', error);
      throw error;
    }
  },

  logout: async () => {
    try {
      // Get token from localStorage
      let token;
      if (typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token');
        // Clear token from localStorage
        localStorage.removeItem('auth_token');

        // Clear token from API headers
        if (api.defaults.headers) {
          delete api.defaults.headers.common['Authorization'];
        }

        // Clear token from cookies
        document.cookie = 'token=; path=/; max-age=0; SameSite=Strict';
      }

      console.log('Logging out user');
      const headers = token ? { Authorization: `Bearer ${token}` } : {};
      const response = await api.get('/logout', {
        headers,
        withCredentials: true // This ensures cookies are sent with the request
      });
      console.log('Logout response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error logging out:', error);
      // Even if the API call fails, we want to clear local storage and cookies
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        if (api.defaults.headers) {
          delete api.defaults.headers.common['Authorization'];
        }
        document.cookie = 'token=; path=/; max-age=0; SameSite=Strict';
      }
      throw error;
    }
  },

  getCurrentUser: async (token?: string) => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      console.log('Getting current user with token:', token ? `${token.substring(0, 10)}...` : 'No token');
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Make sure we're using the correct endpoint
      const response = await fetch('/api/me', {
        method: 'GET',
        headers,
      });
      const data = await response.json();

      console.log('Get user response:', data);

      // If successful, store user data in localStorage for fallback
      if (data.success && data.user && typeof window !== 'undefined') {
        // Only store user data if the user has "user" role or no role (defaults to "user")
        if (!data.user.role || data.user.role === "user") {
          localStorage.setItem('user_data', JSON.stringify(data.user));
        } else {
          console.log('authService.getCurrentUser: User has non-user role:', data.user.role, 'Not storing data');
          throw new Error('Access denied: This application is only for regular users');
        }
      }

      return data;
    } catch (error) {
      console.error('Error getting current user:', error);
      throw error;
    }
  },

  updateUserProfile: async (userData: {
    name?: string;
    email?: string;
    address?: string;
    location?: {
      coordinates?: { lat: number; lng: number };
      formattedAddress?: string;
    };
  }, token?: string) => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      console.log('Updating user profile with data:', userData);
      const headers = token ? { Authorization: `Bearer ${token}` } : {};

      // Make sure we're using the correct endpoint and sending cookies
      const response = await api.put('/profile', userData, {
        headers,
        withCredentials: true // This ensures cookies are sent with the request
      });

      console.log('Update profile response:', response.data);

      // If successful, update user data in localStorage for fallback
      if (response.data.success && response.data.user && typeof window !== 'undefined') {
        // Only store user data if the user has "user" role or no role (defaults to "user")
        if (!response.data.user.role || response.data.user.role === "user") {
          // Get existing user data
          const existingUserData = localStorage.getItem('user_data');
          if (existingUserData) {
            try {
              const parsedUserData = JSON.parse(existingUserData);
              // Update with new data
              const updatedUserData = {
                ...parsedUserData,
                ...userData
              };
              localStorage.setItem('user_data', JSON.stringify(updatedUserData));
            } catch (e) {
              console.error('Error updating user data in localStorage:', e);
              // If parsing fails, just store the new user data
              localStorage.setItem('user_data', JSON.stringify(response.data.user));
            }
          } else {
            // If no existing data, store the new user data
            localStorage.setItem('user_data', JSON.stringify(response.data.user));
          }
        } else {
          console.log('authService.updateUserProfile: User has non-user role:', response.data.user.role, 'Not storing data');
          throw new Error('Access denied: This application is only for regular users');
        }
      }

      return response.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  forgotPassword: async (email: string) => {
    try {
      console.log('Requesting password reset for email:', email);
      const response = await api.post('/api/users/password/forgot', { email });
      console.log('Forgot password response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error requesting password reset:', error);
      throw error;
    }
  },

  resetPassword: async (token: string, passwords: { password: string; confirmPassword: string }) => {
    try {
      console.log('Resetting password with token:', token.substring(0, 10) + '...');
      const response = await api.put(`/api/users/password/reset/${token}`, passwords);
      console.log('Reset password response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
  },

  uploadProfilePhoto: async (file: File, token?: string) => {
    try {
      // Try to get token from localStorage if not provided
      if (!token && typeof window !== 'undefined') {
        token = localStorage.getItem('auth_token') || undefined;
      }

      console.log('Uploading profile photo:', file.name, file.size, file.type);
      const formData = new FormData();
      formData.append('profilePhoto', file);

      // Log FormData contents for debugging
      console.log('FormData entries:');
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      // Create a separate axios instance for file uploads without default Content-Type
      const uploadApi = axios.create({
        baseURL: API_URL,
        timeout: 30000,
        withCredentials: true,
        // Don't set default Content-Type for file uploads
      });

      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      console.log('Upload headers:', headers);

      const response = await uploadApi.post('/upload-photo', formData, {
        headers
      });

      console.log('Upload photo response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error uploading profile photo:', error);
      throw error;
    }
  }
};

export default api;
